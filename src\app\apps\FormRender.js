import React, { useState } from 'react';
import { Nav, Tab } from 'react-bootstrap';
import { STATIC_URL } from '../constants';

const FormRender = ({ formData }) => {
    // Collect all checklist-group items
    const checklistGroups = formData.filter(item => item.type === 'checklist-group');
    const [activeTab, setActiveTab] = useState(checklistGroups.length > 0 ? checklistGroups[0].label : '');

    const styles = {
        formContainer: {

        },
        formGroup: {
            marginBottom: '15px',
        },
        label: {
            fontWeight: 'bold',
            display: 'block',
            marginBottom: '5px',
        },
        textDisplay: {
            padding: '8px',
            margin: '5px 0',
            background: '#e9ecef',
            borderRadius: '4px',
        },
        imageList: {
            listStyleType: 'none',
            paddingLeft: '0',
        },
        imageItem: {
            borderRadius: '4px',
            margin: '5px 0',
            maxWidth: '100%', // Ensure images are responsive
        },
        uncheckedTab: {
            backgroundColor: '#dc3545',
            color: 'white',
            borderColor: '#dc3545'
        },
        checkedTab: {
            backgroundColor: '#28a745',
            color: 'white',
            borderColor: '#28a745'
        },
        noInspectionMessage: {
            textAlign: 'center',
            padding: '2rem',
            color: '#dc3545',
            fontWeight: 'bold',
            fontSize: '1.1rem'
        }
    };

    const renderImages = (imageUrls) => {
        if (imageUrls) {
            const urls = imageUrls.split(', ');
            return (
                <ul style={styles.imageList}>
                    {urls.map((url, index) => (
                        <li key={index}>
                            <img src={`${STATIC_URL}/${url}`} alt={`Uploaded content ${index + 1}`} style={styles.imageItem} />
                        </li>
                    ))}
                </ul>
            );
        }
        return null;
    };

    return (
        <div style={styles.formContainer}>
            {/* Render checklist groups as tabs if they exist */}
            {checklistGroups.length > 0 && (
                <div className="mb-4">
                    <h4 className="mb-3">Inspection Checklist</h4>
                    <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k)}>
                        <Nav variant="pills" className="mb-3">
                            {checklistGroups.map((group, index) => {
                                const label = group.label || 'No Label';
                                const isChecked = group.checked;

                                return (
                                    <Nav.Item key={index}>
                                        <Nav.Link
                                            eventKey={label}
                                            style={isChecked ? styles.checkedTab : styles.uncheckedTab}
                                            className="me-2 mb-2"
                                        >
                                            <span dangerouslySetInnerHTML={{ __html: label }} />
                                        </Nav.Link>
                                    </Nav.Item>
                                );
                            })}
                        </Nav>

                        <Tab.Content>
                            {checklistGroups.map((group, index) => {
                                const label = group.label || 'No Label';
                                const questions = group.questions || [];
                                const isChecked = group.checked;

                                return (
                                    <Tab.Pane key={index} eventKey={label}>
                                        {isChecked ? (
                                            <div>
                                                {questions.length > 0 && (
                                                    <div>
                                                        {questions.map((question, qIndex) => {
                                                            const questionLabel = question.label || 'No Question Label';
                                                            const options = question.options || [];

                                                            return (
                                                                <div key={qIndex} className='boxShadow p-2 mt-3'>
                                                                    <div>Q . {questionLabel}</div>
                                                                    {options.length > 0 && (
                                                                        <div className='mt-3'>
                                                                            {options.map((option, oIndex) => {
                                                                                if (option.checked === 1) {
                                                                                    return (
                                                                                        <div key={oIndex} className='fw-bold'>
                                                                                            {option.label}
                                                                                        </div>
                                                                                    );
                                                                                } else {
                                                                                    return null;
                                                                                }
                                                                            })}
                                                                        </div>
                                                                    )}

                                                                    <div className='fw-bold mt-2'>Remarks</div>
                                                                    <p>{question.remarks}</p>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                        ) : (
                                            <div style={styles.noInspectionMessage}>
                                                No Inspection carried out in this section
                                            </div>
                                        )}
                                    </Tab.Pane>
                                );
                            })}
                        </Tab.Content>
                    </Tab.Container>
                </div>
            )}

            {/* Render other form items */}
            {formData.map((item, index) => {
                if (item.type === "textarea") {
                    return (
                        <div key={index} className='boxShadow mt-3 mb-3 p-2'>
                            <div>{item.label}</div>
                            <div className='fw-bold mt-2'>{item.value || ''}</div>
                        </div>
                    )
                } else if (item.type === "sign") {
                    return (
                        <div key={index} className='boxShadow mt-2 mb-3 p-2 '>
                            <div>{item.label}</div>
                            <img src={`${STATIC_URL}/${item.value}`} width={100} alt="Signature" />
                        </div>
                    )
                }
                return null;
            })}
        </div>
    );
};

export default FormRender;
